<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Security language settings
return [
    'disallowedAction' => 'The action you requested is not allowed.',
    'insecureCookie'   => 'Attempted to send a secure cookie over a non-secure connection.',

    // @deprecated
    'invalidSameSite' => 'The SameSite value must be None, Lax, Strict, or a blank string. Given: "{0}"',
];
