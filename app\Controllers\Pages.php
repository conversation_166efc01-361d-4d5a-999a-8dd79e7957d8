<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class Pages extends BaseController
{
    public function aboutUs()
    {
        return $this->renderPage('pages/about_us', 'About', 'about-us');
    }

    public function services()
    {
        return $this->renderPage('pages/services', 'Services', 'services');
    }

    public function trainingProgram()
    {
        return $this->renderPage('pages/training_program', 'Training Program', 'training-program');
    }

    public function joinOurTeam()
    {
        return $this->renderPage('pages/join_our_team', 'Join Our Team', 'join-our-team');
    }

    public function contact()
    {
        return $this->renderPage('pages/contact', 'Contact', 'contact');
    }

    private function renderPage(string $view, string $title, string $page = '')
    {
        echo view('templates/header', ['title' => $title, 'page' => $page]);
        echo view($view);
        echo view('templates/footer');
    }
}


