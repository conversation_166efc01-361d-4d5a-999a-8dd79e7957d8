CRITICAL - 2025-08-19 16:54:25 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/home.php"
[Method: GET, Route: /]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/home.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/home', [], true)
 3 APPPATH\Controllers\Home.php(12): view('pages/home')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:00:14 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/home/<USER>"
[Method: GET, Route: /]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/home/<USER>')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/home/<USER>', [], true)
 3 APPPATH\Views\pages\home.php(1): view('pages/home/<USER>')
 4 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\cosine\\app\\Views\\pages\\home.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/home', [], true)
 7 APPPATH\Controllers\Home.php(12): view('pages/home')
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:00:15 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/home/<USER>"
[Method: GET, Route: /]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/home/<USER>')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/home/<USER>', [], true)
 3 APPPATH\Views\pages\home.php(1): view('pages/home/<USER>')
 4 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\cosine\\app\\Views\\pages\\home.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/home', [], true)
 7 APPPATH\Controllers\Home.php(12): view('pages/home')
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:02:11 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/home/<USER>"
[Method: GET, Route: /]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/home/<USER>')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/home/<USER>', [], true)
 3 APPPATH\Views\pages\home.php(1): view('pages/home/<USER>')
 4 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\cosine\\app\\Views\\pages\\home.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/home', [], true)
 7 APPPATH\Controllers\Home.php(12): view('pages/home')
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:03:21 --> TypeError: App\Controllers\Home::index(): Return value must be of type string, none returned
[Method: GET, Route: /]
in APPPATH\Controllers\Home.php on line 14.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:04:56 --> TypeError: App\Controllers\Home::index(): Return value must be of type string, none returned
[Method: GET, Route: /]
in APPPATH\Controllers\Home.php on line 14.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:06:41 --> TypeError: App\Controllers\Home::index(): Return value must be of type string, none returned
[Method: GET, Route: /]
in APPPATH\Controllers\Home.php on line 14.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:07:45 --> TypeError: App\Controllers\Home::index(): Return value must be of type string, none returned
[Method: GET, Route: /]
in APPPATH\Controllers\Home.php on line 14.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:07:55 --> TypeError: App\Controllers\Home::index(): Return value must be of type string, none returned
[Method: GET, Route: /]
in APPPATH\Controllers\Home.php on line 14.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:14:49 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/partials/marquee.php"
[Method: GET, Route: about]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/partials/marquee.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/partials/marquee', [], true)
 3 APPPATH\Views\pages\about.php(238): view('pages/partials/marquee')
 4 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\cosine\\app\\Views\\pages\\about.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/about', [], true)
 7 APPPATH\Controllers\Pages.php(37): view('pages/about')
 8 APPPATH\Controllers\Pages.php(11): App\Controllers\Pages->renderPage('pages/about', 'About', 'about')
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Pages->about()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Pages))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:14:51 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/partials/marquee.php"
[Method: GET, Route: about]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/partials/marquee.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/partials/marquee', [], true)
 3 APPPATH\Views\pages\about.php(238): view('pages/partials/marquee')
 4 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\cosine\\app\\Views\\pages\\about.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/about', [], true)
 7 APPPATH\Controllers\Pages.php(37): view('pages/about')
 8 APPPATH\Controllers\Pages.php(11): App\Controllers\Pages->renderPage('pages/about', 'About', 'about')
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Pages->about()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Pages))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:17:10 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/partials/marquee.php"
[Method: GET, Route: about-us]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/partials/marquee.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/partials/marquee', [], true)
 3 APPPATH\Views\pages\about_us.php(238): view('pages/partials/marquee')
 4 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\cosine\\app\\Views\\pages\\about_us.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/about_us', [], true)
 7 APPPATH\Controllers\Pages.php(37): view('pages/about_us')
 8 APPPATH\Controllers\Pages.php(11): App\Controllers\Pages->renderPage('pages/about_us', 'About', 'about-us')
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Pages->aboutUs()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Pages))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-08-19 17:17:34 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/partials/marquee.php"
[Method: GET, Route: about-us]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/partials/marquee.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/partials/marquee', [], true)
 3 APPPATH\Views\pages\about_us.php(238): view('pages/partials/marquee')
 4 SYSTEMPATH\View\View.php(224): include('C:\\xampp\\htdocs\\cosine\\app\\Views\\pages\\about_us.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/about_us', [], true)
 7 APPPATH\Controllers\Pages.php(37): view('pages/about_us')
 8 APPPATH\Controllers\Pages.php(11): App\Controllers\Pages->renderPage('pages/about_us', 'About', 'about-us')
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Pages->aboutUs()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Pages))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
