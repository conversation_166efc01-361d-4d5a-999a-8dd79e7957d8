/*!
 * Justified Gallery - v3.6.1
 * http://miromannino.github.io/Justified-Gallery/
 * Copyright (c) 2015 Mir<PERSON>
 * Licensed under the MIT license.
 */
@-webkit-keyframes justified-gallery-show-caption-animation {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.7;
  }
}
@-moz-keyframes justified-gallery-show-caption-animation {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.7;
  }
}
@-o-keyframes justified-gallery-show-caption-animation {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.7;
  }
}
@keyframes justified-gallery-show-caption-animation {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.7;
  }
}
@-webkit-keyframes justified-gallery-show-entry-animation {
  from {
    opacity: 0;
  }
  to {
    opacity: 1.0;
  }
}
@-moz-keyframes justified-gallery-show-entry-animation {
  from {
    opacity: 0;
  }
  to {
    opacity: 1.0;
  }
}
@-o-keyframes justified-gallery-show-entry-animation {
  from {
    opacity: 0;
  }
  to {
    opacity: 1.0;
  }
}
@keyframes justified-gallery-show-entry-animation {
  from {
    opacity: 0;
  }
  to {
    opacity: 1.0;
  }
}
.justified-gallery {
  width: 100%;
  position: relative;
  overflow: hidden;
}
.justified-gallery > a,
.justified-gallery > div {
  position: absolute;
  display: inline-block;
  overflow: hidden;
  opacity: 0;
  filter: alpha(opacity=0);
  /* IE8 or Earlier */
}
.justified-gallery > a > img,
.justified-gallery > div > img,
.justified-gallery > a > a > img,
.justified-gallery > div > a > img {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: 0;
  padding: 0;
  border: none;
}
.justified-gallery > a > .caption,
.justified-gallery > div > .caption {
  display: none;
  position: absolute;
  bottom: 0;
  padding: 5px;
  background-color: #000000;
  left: 0;
  right: 0;
  margin: 0;
  color: white;
  font-size: 12px;
  font-weight: 300;
  font-family: sans-serif;
}
.justified-gallery > a > .caption.caption-visible,
.justified-gallery > div > .caption.caption-visible {
  display: initial;
  opacity: 0.7;
  filter: "alpha(opacity=70)";
  /* IE8 or Earlier */
  -webkit-animation: justified-gallery-show-caption-animation 500ms 0 ease;
  -moz-animation: justified-gallery-show-caption-animation 500ms 0 ease;
  -ms-animation: justified-gallery-show-caption-animation 500ms 0 ease;
}
.justified-gallery > .entry-visible {
  opacity: 1.0;
  filter: alpha(opacity=100);
  /* IE8 or Earlier */
  -webkit-animation: justified-gallery-show-entry-animation 500ms 0 ease;
  -moz-animation: justified-gallery-show-entry-animation 500ms 0 ease;
  -ms-animation: justified-gallery-show-entry-animation 500ms 0 ease;
}
.justified-gallery > .jg-filtered {
  display: none;
}
.justified-gallery > .spinner {
  position: absolute;
  bottom: 0;
  margin-left: -24px;
  padding: 10px 0 10px 0;
  left: 50%;
  opacity: initial;
  filter: initial;
  overflow: initial;
}
.justified-gallery > .spinner > span {
  display: inline-block;
  opacity: 0;
  filter: alpha(opacity=0);
  /* IE8 or Earlier */
  width: 8px;
  height: 8px;
  margin: 0 4px 0 4px;
  background-color: #000;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}
